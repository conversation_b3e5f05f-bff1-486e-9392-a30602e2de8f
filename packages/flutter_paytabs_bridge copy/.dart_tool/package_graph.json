{"roots": ["flutter_paytabs_bridge"], "packages": [{"name": "flutter_paytabs_bridge", "version": "2.6.9", "dependencies": ["flutter", "path_provider"], "devDependencies": ["flutter_test"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["clock", "collection", "fake_async", "flutter", "leak_tracker_flutter_testing", "matcher", "meta", "path", "stack_trace", "stream_channel", "test_api", "vector_math"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "leak_tracker_flutter_testing", "version": "3.0.10", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.2.0", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "path_provider_windows", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "win32"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.3.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "async", "version": "2.11.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker", "version": "11.0.1", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "term_glyph", "version": "1.2.1", "dependencies": []}, {"name": "string_scanner", "version": "1.2.0", "dependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.0", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "boolean_selector", "version": "2.1.1", "dependencies": ["source_span", "string_scanner"]}, {"name": "win32", "version": "5.3.0", "dependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.2", "dependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "platform", "version": "3.1.4", "dependencies": []}, {"name": "xdg_directories", "version": "1.0.4", "dependencies": ["meta", "path"]}, {"name": "vm_service", "version": "13.0.0", "dependencies": []}, {"name": "leak_tracker_testing", "version": "3.0.2", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "path_provider_android", "version": "2.2.18", "dependencies": ["flutter", "path_provider_platform_interface"]}], "configVersion": 1}