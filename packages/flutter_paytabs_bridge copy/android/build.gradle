group 'com.paymentsdk.flutter_paymentsdk_bridge'
version '1.0'


buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
    }
}

apply plugin: 'com.android.library'

android {
    compileSdk 34

    defaultConfig {
        minSdkVersion 21
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    configurations.configureEach {
        resolutionStrategy {
            exclude group: "org.jetbrains.kotlinx", module: "kotlinx-coroutines-debug"
        }
    }
    namespace 'com.paymentsdk.flutter_paymentsdk_bridge'
}

dependencies {
    implementation 'com.paytabs:payment-sdk:6.5.9'
}