group 'com.paymentsdk.flutter_paymentsdk_bridge'
version '1.0'


buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.12.1'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 36

    defaultConfig {
        minSdkVersion 21
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    namespace 'com.paytabs.flutter_payment_sdk_bridge_example'
}

dependencies {
    implementation "com.paytabs:payment-sdk:6.8.1"
    implementation "androidx.appcompat:appcompat:1.7.1"
    implementation "com.google.code.gson:gson:2.13.1"
}